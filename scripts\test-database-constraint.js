#!/usr/bin/env node

/**
 * <PERSON>ript to test the database constraint directly
 * 
 * Run with: node scripts/test-database-constraint.js
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
dotenv.config({ path: join(__dirname, '..', '.env.local') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function testDatabaseConstraint() {
  console.log('🔍 Testing Database Constraint Directly');
  console.log('======================================\n');

  try {
    // Get a test employee
    const { data: employees, error: empError } = await supabase
      .from('employees')
      .select('id, name')
      .eq('is_active', true)
      .neq('name', 'Qualsiasi')
      .limit(1);

    if (empError || !employees || employees.length === 0) {
      throw new Error('No employees found for testing');
    }

    const employee = employees[0];
    const testDate = '2025-01-21';
    const testTime = '15:00';

    console.log(`👤 Test employee: ${employee.name} (${employee.id})`);
    console.log(`📅 Test date: ${testDate}`);
    console.log(`⏰ Test time: ${testTime}\n`);

    // Clean up any existing test appointments
    console.log('🧹 Cleaning up existing test appointments...');
    await supabase
      .from('appointments')
      .delete()
      .eq('data_appuntamento', testDate)
      .eq('employee_id', employee.id);

    // Test 1: Create first appointment
    console.log('📋 Test 1: Creating first appointment...');
    const { data: apt1, error: createError1 } = await supabase
      .from('appointments')
      .insert({
        nome: 'Test',
        cognome: 'User1',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: 'Servizi CAF',
        prestazione: 'Test Service',
        operatore: employee.name,
        employee_id: employee.id,
        data_appuntamento: testDate,
        orario: testTime,
        custom_time_range: false,
        status: 'confirmed'
      })
      .select()
      .single();

    if (createError1) {
      throw new Error(`Failed to create first appointment: ${createError1.message}`);
    }

    console.log(`✅ First appointment created successfully (ID: ${apt1.id})`);

    // Test 2: Try to create duplicate appointment (should fail)
    console.log('\n📋 Test 2: Attempting to create duplicate appointment...');
    const { data: apt2, error: createError2 } = await supabase
      .from('appointments')
      .insert({
        nome: 'Test',
        cognome: 'User2',
        telefono: '1234567890',
        email: '<EMAIL>',
        servizio: 'Servizi CAF',
        prestazione: 'Test Service',
        operatore: employee.name,
        employee_id: employee.id,
        data_appuntamento: testDate,
        orario: testTime,
        custom_time_range: false,
        status: 'confirmed'
      })
      .select()
      .single();

    if (createError2) {
      console.log(`✅ Duplicate appointment correctly prevented!`);
      console.log(`   Error: ${createError2.message}`);
      console.log(`   Code: ${createError2.code}`);
      
      if (createError2.code === '23505') {
        console.log('   ✅ This is the expected unique constraint violation');
      } else {
        console.log('   ⚠️  Unexpected error code');
      }
    } else {
      console.log(`❌ Duplicate appointment was NOT prevented!`);
      console.log(`   Second appointment created with ID: ${apt2.id}`);
      
      // Clean up the duplicate
      await supabase
        .from('appointments')
        .delete()
        .eq('id', apt2.id);
    }

    // Test 3: Try to create appointment for different employee at same time (should succeed)
    const { data: otherEmployees, error: otherEmpError } = await supabase
      .from('employees')
      .select('id, name')
      .eq('is_active', true)
      .neq('name', 'Qualsiasi')
      .neq('id', employee.id)
      .limit(1);

    if (!otherEmpError && otherEmployees && otherEmployees.length > 0) {
      const otherEmployee = otherEmployees[0];
      
      console.log(`\n📋 Test 3: Creating appointment for different employee at same time...`);
      console.log(`👤 Other employee: ${otherEmployee.name} (${otherEmployee.id})`);
      
      const { data: apt3, error: createError3 } = await supabase
        .from('appointments')
        .insert({
          nome: 'Test',
          cognome: 'User3',
          telefono: '1234567890',
          email: '<EMAIL>',
          servizio: 'Servizi CAF',
          prestazione: 'Test Service',
          operatore: otherEmployee.name,
          employee_id: otherEmployee.id,
          data_appuntamento: testDate,
          orario: testTime,
          custom_time_range: false,
          status: 'confirmed'
        })
        .select()
        .single();

      if (createError3) {
        console.log(`❌ Appointment for different employee was prevented (this is wrong!)`);
        console.log(`   Error: ${createError3.message}`);
      } else {
        console.log(`✅ Appointment for different employee created successfully (ID: ${apt3.id})`);
        
        // Clean up
        await supabase
          .from('appointments')
          .delete()
          .eq('id', apt3.id);
      }
    }

    // Clean up the first appointment
    console.log('\n🧹 Cleaning up test appointments...');
    await supabase
      .from('appointments')
      .delete()
      .eq('id', apt1.id);

    console.log('\n🎉 Database constraint test completed!');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    return false;
  }
}

// Run the test
testDatabaseConstraint().catch(console.error);
