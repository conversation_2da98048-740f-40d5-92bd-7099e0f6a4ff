-- ========================================
-- CAF APPOINTMENT BOOKING SYSTEM
-- COMPLETE DATABASE SCHEMA
-- ========================================
-- This script creates the complete database schema for the CAF appointment booking system
-- Run this in your Supabase SQL Editor to set up the entire database from scratch
-- 
-- Features included:
-- - Appointment management with full CRUD operations
-- - Employee management system with specializations
-- - Employee availability scheduling with break management
-- - Special schedules for holidays and exceptions
-- - Admin user authentication
-- - Row Level Security (RLS) policies
-- - Performance indexes and constraints
-- - Utility functions for availability checking
-- - Database triggers for automatic updates
-- ========================================

-- Enable Row Level Security globally
ALTER DATABASE postgres SET "app.jwt_secret" TO 'caf-admin-secret-key-2024-montesacro';

-- ========================================
-- CORE TABLES
-- ========================================

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id BIGSERIAL PRIMARY KEY,
    nome VARCHAR(100) NOT NULL,
    cognome VARCHAR(100) NOT NULL,
    telefono VARCHAR(20) DEFAULT 'Non registrato',
    email VARCHAR(255) DEFAULT 'Non registrato',
    servizio VARCHAR(255) NOT NULL,
    prestazione VARCHAR(255),
    operatore VARCHAR(50) DEFAULT 'Qualsiasi',
    note_aggiuntive TEXT,
    data_appuntamento DATE NOT NULL,
    orario TIME NOT NULL,
    employee_id UUID, -- Will be linked after employees table is created
    status VARCHAR(20) DEFAULT 'confirmed' CHECK (status IN ('confirmed', 'completed', 'cancelled', 'no_show')),
    operator_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create admin_users table for authentication
CREATE TABLE IF NOT EXISTS admin_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'admin' CHECK (role IN ('admin', 'super_admin')),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create employees table
CREATE TABLE IF NOT EXISTS employees (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    role VARCHAR(100),
    department VARCHAR(100),
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    specializations TEXT[], -- Array of specializations/services they can handle
    working_hours JSONB DEFAULT '{"monday": {"start": "09:00", "end": "18:00"}, "tuesday": {"start": "09:00", "end": "18:00"}, "wednesday": {"start": "09:00", "end": "18:00"}, "thursday": {"start": "09:00", "end": "18:00"}, "friday": {"start": "09:00", "end": "18:00"}, "saturday": null, "sunday": null}',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create employee_availability table for detailed scheduling
CREATE TABLE IF NOT EXISTS employee_availability (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 1=Monday, ..., 6=Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    break_start_time TIME, -- Optional break time
    break_end_time TIME,   -- Optional break end time
    break_disabled BOOLEAN DEFAULT false, -- Disable break for this day
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    CONSTRAINT valid_day_time_unique UNIQUE (employee_id, day_of_week, start_time, end_time),
    CONSTRAINT valid_break_times CHECK (
        break_disabled = true OR
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    )
);

-- Create employee_special_schedules table for exceptions and holidays
CREATE TABLE IF NOT EXISTS employee_special_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id UUID NOT NULL REFERENCES employees(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    is_available BOOLEAN DEFAULT false,
    start_time TIME,
    end_time TIME,
    break_start_time TIME,
    break_end_time TIME,
    break_disabled BOOLEAN DEFAULT false,
    reason VARCHAR(255),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_special_time_range CHECK (
        NOT is_available OR (start_time IS NOT NULL AND end_time IS NOT NULL AND end_time > start_time)
    ),
    CONSTRAINT valid_special_break_times CHECK (
        break_disabled = true OR
        (break_start_time IS NULL AND break_end_time IS NULL) OR
        (break_start_time IS NOT NULL AND break_end_time IS NOT NULL AND 
         break_end_time > break_start_time AND
         break_start_time >= start_time AND break_end_time <= end_time)
    ),
    CONSTRAINT unique_employee_date UNIQUE (employee_id, date)
);

-- Add foreign key constraint to appointments table
ALTER TABLE appointments ADD CONSTRAINT fk_appointments_employee 
    FOREIGN KEY (employee_id) REFERENCES employees(id);

-- ========================================
-- INDEXES FOR PERFORMANCE
-- ========================================

-- Appointments table indexes
CREATE INDEX IF NOT EXISTS idx_appointments_date_time ON appointments(data_appuntamento, orario);
CREATE INDEX IF NOT EXISTS idx_appointments_status ON appointments(status);
CREATE INDEX IF NOT EXISTS idx_appointments_created_at ON appointments(created_at);
CREATE INDEX IF NOT EXISTS idx_appointments_email ON appointments(email);
CREATE INDEX IF NOT EXISTS idx_appointments_employee_id ON appointments(employee_id);
CREATE INDEX IF NOT EXISTS idx_appointments_prestazione ON appointments(prestazione);
CREATE INDEX IF NOT EXISTS idx_appointments_servizio_prestazione ON appointments(servizio, prestazione);

-- Employee table indexes
CREATE INDEX IF NOT EXISTS idx_employees_active ON employees(is_active);
CREATE INDEX IF NOT EXISTS idx_employees_name ON employees(name);
CREATE INDEX IF NOT EXISTS idx_employees_specializations ON employees USING GIN(specializations);

-- Employee availability indexes
CREATE INDEX IF NOT EXISTS idx_employee_availability_employee_day ON employee_availability(employee_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_employee_availability_time ON employee_availability(start_time, end_time);

-- Employee special schedules indexes
CREATE INDEX IF NOT EXISTS idx_employee_special_schedules_employee_date ON employee_special_schedules(employee_id, date);
CREATE INDEX IF NOT EXISTS idx_employee_special_schedules_date ON employee_special_schedules(date);

-- Admin users indexes
CREATE INDEX IF NOT EXISTS idx_admin_users_username ON admin_users(username);
CREATE INDEX IF NOT EXISTS idx_admin_users_active ON admin_users(is_active);

-- ========================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ========================================

-- Enable Row Level Security on all tables
ALTER TABLE appointments ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_availability ENABLE ROW LEVEL SECURITY;
ALTER TABLE employee_special_schedules ENABLE ROW LEVEL SECURITY;

-- Appointments table policies
CREATE POLICY "Allow public to insert appointments" ON appointments
    FOR INSERT TO anon
    WITH CHECK (true);

CREATE POLICY "Allow public to read appointments for availability" ON appointments
    FOR SELECT TO anon
    USING (true);

CREATE POLICY "Allow anon to update appointments" ON appointments
    FOR UPDATE TO anon
    USING (true)
    WITH CHECK (true);

CREATE POLICY "Allow anon to delete appointments" ON appointments
    FOR DELETE TO anon
    USING (true);

-- Admin users table policies
CREATE POLICY "Allow authenticated users to read admin_users" ON admin_users
    FOR SELECT TO authenticated
    USING (true);

CREATE POLICY "Allow users to update own profile" ON admin_users
    FOR UPDATE TO authenticated
    USING (auth.uid()::text = id::text)
    WITH CHECK (auth.uid()::text = id::text);

-- Employees table policies
CREATE POLICY "Allow anon full access to employees" ON employees
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Employee availability table policies
CREATE POLICY "Allow anon full access to employee_availability" ON employee_availability
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- Employee special schedules table policies
CREATE POLICY "Allow anon full access to employee_special_schedules" ON employee_special_schedules
    FOR ALL TO anon
    USING (true)
    WITH CHECK (true);

-- ========================================
-- DATABASE FUNCTIONS AND TRIGGERS
-- ========================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at on all tables
CREATE TRIGGER update_appointments_updated_at
    BEFORE UPDATE ON appointments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_users_updated_at
    BEFORE UPDATE ON admin_users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employees_updated_at
    BEFORE UPDATE ON employees
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_availability_updated_at
    BEFORE UPDATE ON employee_availability
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employee_special_schedules_updated_at
    BEFORE UPDATE ON employee_special_schedules
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to check employee availability
CREATE OR REPLACE FUNCTION is_employee_available(
    p_employee_id UUID,
    p_date DATE,
    p_time TIME
) RETURNS BOOLEAN AS $$
DECLARE
    v_day_of_week INTEGER;
    v_special_schedule RECORD;
    v_regular_availability RECORD;
BEGIN
    -- Get day of week (0=Sunday, 1=Monday, etc.)
    v_day_of_week := EXTRACT(DOW FROM p_date);

    -- Check for special schedule first (overrides regular availability)
    SELECT * INTO v_special_schedule
    FROM employee_special_schedules
    WHERE employee_id = p_employee_id AND date = p_date;

    IF FOUND THEN
        -- Special schedule exists
        IF NOT v_special_schedule.is_available THEN
            RETURN FALSE;
        END IF;

        -- Check if time is within special schedule hours
        IF p_time < v_special_schedule.start_time OR p_time >= v_special_schedule.end_time THEN
            RETURN FALSE;
        END IF;

        -- Check if time is during break (only if break is not disabled)
        IF NOT COALESCE(v_special_schedule.break_disabled, false) AND
           v_special_schedule.break_start_time IS NOT NULL AND
           p_time >= v_special_schedule.break_start_time AND
           p_time < v_special_schedule.break_end_time THEN
            RETURN FALSE;
        END IF;

        RETURN TRUE;
    END IF;

    -- No special schedule, check regular availability
    SELECT * INTO v_regular_availability
    FROM employee_availability
    WHERE employee_id = p_employee_id
      AND day_of_week = v_day_of_week
      AND is_available = true
      AND p_time >= start_time
      AND p_time < end_time;

    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;

    -- Check if time is during break (only if break is not disabled)
    IF NOT COALESCE(v_regular_availability.break_disabled, false) AND
       v_regular_availability.break_start_time IS NOT NULL AND
       p_time >= v_regular_availability.break_start_time AND
       p_time < v_regular_availability.break_end_time THEN
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to get appointment statistics
CREATE OR REPLACE FUNCTION get_appointment_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
    today_start DATE := CURRENT_DATE;
    today_end DATE := CURRENT_DATE + INTERVAL '1 day';
    week_start DATE := CURRENT_DATE - EXTRACT(DOW FROM CURRENT_DATE)::INTEGER;
    week_end DATE := week_start + INTERVAL '7 days';
    month_start DATE := DATE_TRUNC('month', CURRENT_DATE);
    month_end DATE := month_start + INTERVAL '1 month';
BEGIN
    SELECT json_build_object(
        'total', (SELECT COUNT(*) FROM appointments),
        'today', (SELECT COUNT(*) FROM appointments WHERE data_appuntamento = today_start),
        'thisWeek', (SELECT COUNT(*) FROM appointments WHERE data_appuntamento BETWEEN week_start AND week_end),
        'thisMonth', (SELECT COUNT(*) FROM appointments WHERE data_appuntamento BETWEEN month_start AND month_end),
        'byService', (
            SELECT json_object_agg(servizio, count)
            FROM (
                SELECT servizio, COUNT(*) as count
                FROM appointments
                GROUP BY servizio
            ) service_counts
        ),
        'byStatus', (
            SELECT json_object_agg(status, count)
            FROM (
                SELECT status, COUNT(*) as count
                FROM appointments
                GROUP BY status
            ) status_counts
        ),
        'recent', (
            SELECT json_agg(
                json_build_object(
                    'id', id,
                    'nome', nome,
                    'cognome', cognome,
                    'servizio', servizio,
                    'dataAppuntamento', data_appuntamento,
                    'orario', orario,
                    'status', status
                )
            )
            FROM (
                SELECT id, nome, cognome, servizio, data_appuntamento, orario, status
                FROM appointments
                ORDER BY created_at DESC
                LIMIT 5
            ) recent_appointments
        )
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- ========================================
-- DATABASE VIEWS
-- ========================================

-- View for active employees (useful for API queries)
CREATE OR REPLACE VIEW active_employees AS
SELECT
    id,
    name,
    email,
    role,
    department,
    phone,
    specializations,
    working_hours,
    notes,
    created_at,
    updated_at
FROM employees
WHERE is_active = true
ORDER BY name;

-- View for employee availability with day names
CREATE OR REPLACE VIEW employee_availability_view AS
SELECT
    ea.id,
    ea.employee_id,
    e.name as employee_name,
    ea.day_of_week,
    CASE ea.day_of_week
        WHEN 0 THEN 'Domenica'
        WHEN 1 THEN 'Lunedì'
        WHEN 2 THEN 'Martedì'
        WHEN 3 THEN 'Mercoledì'
        WHEN 4 THEN 'Giovedì'
        WHEN 5 THEN 'Venerdì'
        WHEN 6 THEN 'Sabato'
    END as day_name,
    ea.start_time,
    ea.end_time,
    ea.break_start_time,
    ea.break_end_time,
    ea.break_disabled,
    ea.is_available,
    ea.notes,
    ea.created_at,
    ea.updated_at
FROM employee_availability ea
JOIN employees e ON ea.employee_id = e.id
WHERE e.is_active = true
ORDER BY e.name, ea.day_of_week, ea.start_time;

-- ========================================
-- PERMISSIONS AND GRANTS
-- ========================================

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON appointments TO anon, authenticated;
GRANT ALL ON admin_users TO authenticated;
GRANT ALL ON employees TO anon, authenticated;
GRANT ALL ON employee_availability TO anon, authenticated;
GRANT ALL ON employee_special_schedules TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Grant access to views
GRANT SELECT ON active_employees TO anon, authenticated;
GRANT SELECT ON employee_availability_view TO anon, authenticated;

-- ========================================
-- SEED DATA
-- ========================================

-- Insert default admin user (password will be set via environment variables)
INSERT INTO admin_users (username, email, password_hash, role)
VALUES (
    'admin',
    '<EMAIL>',
    '$2b$10$placeholder.hash.will.be.replaced.by.application', -- Placeholder - will be handled in the application
    'admin'
) ON CONFLICT (username) DO NOTHING;

-- Insert default employees
INSERT INTO employees (id, name, email, role, department, phone, specializations, is_active) VALUES
    (gen_random_uuid(), 'Qualsiasi', '<EMAIL>', 'Operatore Generico', 'Generale', NULL,
     ARRAY['Servizi CAF', 'Patronato', 'Avvocato', 'Sportello Immigrazione'], true),
    (gen_random_uuid(), 'Mario Rossi', '<EMAIL>', 'Consulente CAF', 'Servizi CAF', '06-12345678',
     ARRAY['Servizi CAF', 'Patronato'], true),
    (gen_random_uuid(), 'Giulia Verdi', '<EMAIL>', 'Avvocato', 'Legale', '06-87654321',
     ARRAY['Avvocato'], true),
    (gen_random_uuid(), 'Luca Bianchi', '<EMAIL>', 'Operatore Immigrazione', 'Immigrazione', '06-11223344',
     ARRAY['Sportello Immigrazione'], true)
ON CONFLICT (email) DO NOTHING;

-- Insert default availability for employees (Monday-Friday, 9:00-18:00 with lunch break)
INSERT INTO employee_availability (employee_id, day_of_week, start_time, end_time, break_start_time, break_end_time, is_available)
SELECT
    e.id,
    generate_series(1, 5) as day_of_week, -- Monday to Friday
    '09:00'::TIME as start_time,
    '18:00'::TIME as end_time,
    '12:00'::TIME as break_start_time,
    '15:00'::TIME as break_end_time,
    true as is_available
FROM employees e
WHERE e.name != 'Qualsiasi' -- Skip the "any employee" option
ON CONFLICT (employee_id, day_of_week, start_time, end_time) DO NOTHING;

-- Special handling for "Qualsiasi" employee - available all the time
INSERT INTO employee_availability (employee_id, day_of_week, start_time, end_time, break_disabled, is_available)
SELECT
    e.id,
    generate_series(1, 5) as day_of_week, -- Monday to Friday
    '09:00'::TIME as start_time,
    '18:00'::TIME as end_time,
    true as break_disabled, -- No breaks for "any employee"
    true as is_available
FROM employees e
WHERE e.name = 'Qualsiasi'
ON CONFLICT (employee_id, day_of_week, start_time, end_time) DO NOTHING;

-- ========================================
-- COMMENTS FOR DOCUMENTATION
-- ========================================

-- Table comments
COMMENT ON TABLE appointments IS 'Main appointments table storing all booking information';
COMMENT ON TABLE admin_users IS 'Admin users for system authentication and management';
COMMENT ON TABLE employees IS 'Employee information and specializations';
COMMENT ON TABLE employee_availability IS 'Regular weekly availability schedules for employees';
COMMENT ON TABLE employee_special_schedules IS 'Special schedules and exceptions for specific dates';

-- Column comments for appointments
COMMENT ON COLUMN appointments.telefono IS 'Phone number (optional, defaults to "Non registrato")';
COMMENT ON COLUMN appointments.email IS 'Email address (optional, defaults to "Non registrato")';
COMMENT ON COLUMN appointments.prestazione IS 'Specific service type selected by the user (e.g., ISEE, Modello 730, etc.)';
COMMENT ON COLUMN appointments.operatore IS 'Preferred operator for the appointment';
COMMENT ON COLUMN appointments.note_aggiuntive IS 'Additional notes for the appointment';
COMMENT ON COLUMN appointments.operator_notes IS 'Internal notes added by operators/admin';
COMMENT ON COLUMN appointments.employee_id IS 'Reference to the assigned employee';

-- Column comments for employee availability
COMMENT ON COLUMN employee_availability.day_of_week IS 'Day of week: 0=Sunday, 1=Monday, ..., 6=Saturday';
COMMENT ON COLUMN employee_availability.break_disabled IS 'When true, employee works without breaks on this day';

-- Column comments for special schedules
COMMENT ON COLUMN employee_special_schedules.break_disabled IS 'When true, employee works without breaks on this special date';
COMMENT ON COLUMN employee_special_schedules.reason IS 'Reason for the special schedule (e.g., holiday, training, etc.)';

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Verify table creation
SELECT
    schemaname,
    tablename,
    tableowner
FROM pg_tables
WHERE schemaname = 'public'
    AND tablename IN ('appointments', 'admin_users', 'employees', 'employee_availability', 'employee_special_schedules')
ORDER BY tablename;

-- Verify RLS policies
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd
FROM pg_policies
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- Verify indexes
SELECT
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes
WHERE schemaname = 'public'
    AND tablename IN ('appointments', 'admin_users', 'employees', 'employee_availability', 'employee_special_schedules')
ORDER BY tablename, indexname;

-- ========================================
-- SETUP COMPLETE
-- ========================================

-- Display success message
DO $$
BEGIN
    RAISE NOTICE '========================================';
    RAISE NOTICE 'CAF APPOINTMENT SYSTEM DATABASE SETUP COMPLETE!';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Tables created: appointments, admin_users, employees, employee_availability, employee_special_schedules';
    RAISE NOTICE 'Views created: active_employees, employee_availability_view';
    RAISE NOTICE 'Functions created: is_employee_available, get_appointment_stats, update_updated_at_column';
    RAISE NOTICE 'RLS policies configured for all tables';
    RAISE NOTICE 'Default data inserted: admin user, sample employees, default availability';
    RAISE NOTICE '========================================';
    RAISE NOTICE 'Next steps:';
    RAISE NOTICE '1. Update admin password via application';
    RAISE NOTICE '2. Configure employee availability as needed';
    RAISE NOTICE '3. Test appointment booking functionality';
    RAISE NOTICE '4. Set up environment variables in your application';
    RAISE NOTICE '========================================';
END $$;
