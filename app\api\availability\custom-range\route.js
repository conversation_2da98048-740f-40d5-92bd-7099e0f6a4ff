import { NextResponse } from 'next/server';
import { isTimeSlotAvailable } from '../../../../lib/supabaseAppointmentUtils.js';
import { isWeekday } from '../../../../lib/utils.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const startTime = searchParams.get('startTime');
    const endTime = searchParams.get('endTime');
    const employeeId = searchParams.get('employeeId'); // Optional: check for specific employee

    if (!date || !startTime || !endTime) {
      return NextResponse.json(
        { success: false, message: 'Data, orario di inizio e orario di fine sono obbligatori' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(date)) {
      return NextResponse.json({
        success: false,
        message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì',
        isAvailable: false
      });
    }

    // Check if the date is in the past
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json({
        success: false,
        message: 'Non è possibile prenotare appuntamenti per date passate',
        isAvailable: false
      });
    }

    // Validate time format (HH:MM)
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(startTime) || !timeRegex.test(endTime)) {
      return NextResponse.json({
        success: false,
        message: 'Formato orario non valido (usa HH:MM)',
        isAvailable: false
      });
    }

    // Validate time range
    const start = new Date(`2000-01-01T${startTime}:00`);
    const end = new Date(`2000-01-01T${endTime}:00`);
    
    if (end <= start) {
      return NextResponse.json({
        success: false,
        message: 'L\'orario di fine deve essere successivo all\'orario di inizio',
        isAvailable: false
      });
    }

    // Check business hours (9 AM - 6 PM)
    const businessStart = new Date(`2000-01-01T09:00:00`);
    const businessEnd = new Date(`2000-01-01T18:00:00`);
    
    if (start < businessStart || end > businessEnd) {
      return NextResponse.json({
        success: false,
        message: 'Gli orari devono essere compresi tra le 09:00 e le 18:00',
        isAvailable: false
      });
    }

    // Check for conflicts with existing appointments
    // If employeeId is provided, check only for that employee; otherwise check globally
    const { checkCustomTimeRangeConflicts } = await import('../../../../lib/supabaseAppointmentUtils.js');
    const conflicts = await checkCustomTimeRangeConflicts(date, startTime, endTime, employeeId);

    if (conflicts.length > 0) {
      const conflictMessage = employeeId
        ? `Conflitto con appuntamenti esistenti per il dipendente selezionato: ${conflicts.join(', ')}`
        : `Conflitto con appuntamenti esistenti: ${conflicts.join(', ')}`;

      return NextResponse.json({
        success: false,
        message: conflictMessage,
        isAvailable: false,
        conflicts,
        employeeId
      });
    }

    return NextResponse.json({
      success: true,
      message: employeeId ? 'Orario disponibile per il dipendente selezionato' : 'Orario disponibile',
      isAvailable: true,
      date,
      startTime,
      endTime,
      employeeId,
      duration: calculateDuration(startTime, endTime)
    });

  } catch (error) {
    console.error('Error checking custom time range availability:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Errore interno del server',
        isAvailable: false
      },
      { status: 500 }
    );
  }
}

// Removed duplicate checkTimeRangeConflicts function - now using the one from supabaseAppointmentUtils.js

/**
 * Calculate duration between two times
 * @param {string} startTime - Start time (HH:MM)
 * @param {string} endTime - End time (HH:MM)
 * @returns {string} Duration in format "X hours Y minutes"
 */
function calculateDuration(startTime, endTime) {
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);
  const diffMs = end - start;
  const diffMinutes = Math.floor(diffMs / 60000);
  
  const hours = Math.floor(diffMinutes / 60);
  const minutes = diffMinutes % 60;
  
  if (hours > 0 && minutes > 0) {
    return `${hours} ore e ${minutes} minuti`;
  } else if (hours > 0) {
    return `${hours} ore`;
  } else {
    return `${minutes} minuti`;
  }
}
