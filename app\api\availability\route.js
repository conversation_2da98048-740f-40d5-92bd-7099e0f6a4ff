import { NextResponse } from 'next/server';
import { isTimeSlotAvailable, getAppointmentsByDateRange } from '../../../lib/supabaseAppointmentUtils.js';
import { getAvailableEmployees } from '../../../lib/supabaseAvailabilityUtils.js';
import { timeSlots, isWeekday } from '../../../lib/utils.js';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');
    const employeeId = searchParams.get('employee_id'); // Optional: check availability for specific employee

    if (!date) {
      return NextResponse.json(
        { success: false, message: 'Data obbligatoria' },
        { status: 400 }
      );
    }

    // Validate that the date is a weekday
    if (!isWeekday(date)) {
      return NextResponse.json({
        success: false,
        message: 'Gli appuntamenti sono disponibili solo dal lunedì al venerdì',
        date,
        availability: [],
        availableSlots: [],
        bookedSlots: timeSlots,
        availableEmployees: []
      });
    }

    // Check if the date is in the past
    const selectedDate = new Date(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      return NextResponse.json({
        success: false,
        message: 'Non è possibile prenotare appuntamenti per date passate',
        date,
        availability: [],
        availableSlots: [],
        bookedSlots: timeSlots,
        availableEmployees: []
      });
    }

    // Get existing appointments for the date
    const existingAppointments = await getAppointmentsByDateRange(date, date);
    const bookedTimes = existingAppointments
      .filter(app => app.status !== 'cancelled')
      .map(app => app.orario);

    // Check availability for each time slot based on employee availability
    const availability = await Promise.all(timeSlots.map(async (time) => {
      // Get all employees who are available for this time slot
      let availableEmployees = [];
      try {
        availableEmployees = await getAvailableEmployees(date, time);
      } catch (error) {
        console.error(`Error getting available employees for ${date} ${time}:`, error);
      }

      // Filter out employees who already have appointments at this time
      const employeesWithoutConflicts = [];
      for (const employee of availableEmployees) {
        const hasConflict = await isTimeSlotAvailable(date, time, employee.employee_id);
        if (hasConflict) {
          employeesWithoutConflicts.push(employee);
        }
      }

      const isSlotAvailable = employeesWithoutConflicts.length > 0;
      const isBooked = bookedTimes.includes(time);

      return {
        time,
        available: isSlotAvailable,
        isBooked,
        label: time,
        status: isSlotAvailable ? 'available' : 'booked',
        availableEmployees: employeesWithoutConflicts.map(emp => ({
          id: emp.employee_id,
          name: emp.employee_name,
          role: emp.role,
          department: emp.department,
          specializations: emp.specializations
        })),
        totalEmployeesAvailable: employeesWithoutConflicts.length
      };
    }));

    // If checking for specific employee, filter availability
    if (employeeId) {
      availability.forEach(slot => {
        const employeeAvailable = slot.availableEmployees.some(emp => emp.id === employeeId);
        slot.available = slot.available && employeeAvailable;
        slot.status = slot.available ? 'available' : 'booked';
        if (employeeAvailable) {
          slot.availableEmployees = slot.availableEmployees.filter(emp => emp.id === employeeId);
        } else {
          slot.availableEmployees = [];
        }
      });
    }

    // Separate available and booked slots
    const availableSlots = availability.filter(slot => slot.available).map(slot => slot.time);
    const bookedSlots = availability.filter(slot => !slot.available).map(slot => slot.time);

    // Group slots by time period
    const morningSlots = availability.filter(slot => {
      const hour = parseInt(slot.time.split(':')[0]);
      return hour >= 9 && hour <= 13;
    });

    const afternoonSlots = availability.filter(slot => {
      const hour = parseInt(slot.time.split(':')[0]);
      return hour >= 15 && hour <= 18;
    });

    // Get all unique available employees for the day
    const allAvailableEmployees = new Map();
    availability.forEach(slot => {
      slot.availableEmployees.forEach(emp => {
        if (!allAvailableEmployees.has(emp.id)) {
          allAvailableEmployees.set(emp.id, emp);
        }
      });
    });

    return NextResponse.json({
      success: true,
      date,
      availability,
      availableSlots,
      bookedSlots,
      availableEmployees: Array.from(allAvailableEmployees.values()),
      summary: {
        total: timeSlots.length,
        available: availableSlots.length,
        booked: bookedSlots.length,
        morningAvailable: morningSlots.filter(s => s.available).length,
        afternoonAvailable: afternoonSlots.filter(s => s.available).length,
        employeesAvailable: allAvailableEmployees.size
      },
      timeGroups: {
        morning: morningSlots,
        afternoon: afternoonSlots
      }
    });

  } catch (error) {
    console.error('Error checking availability:', error);
    return NextResponse.json(
      { success: false, message: 'Errore del server' },
      { status: 500 }
    );
  }
}